<template>
  <gms-dialog
    :destroy-on-close="true"
    :visible="visible"
    width="65%"
    :title="dialogTitle"
    @closed="$emit('update:visible', false)"
  >
    <div :key="formKey" class="container">
      <form-section :title="t('geekplus.gms.client.screen.areaEditIndex.basicInfo')" :columns="3">
        <!-- 区域编码 -->
        <form-item v-bind="formGroup.hostCellCode" :disabled="mode === 'view'" />
        <!-- 区域名称 -->
        <form-item v-bind="formGroup.areaName" :disabled="mode === 'view'" />
      </form-section>
    </div>
  </gms-dialog>
</template>
<script>
import { defineComponent, onMounted, reactive, computed, ref } from "vue";
import {
  createFormItemGroup,
  FormItem,
  formItemTypes,
  FormSection,
  validators,
} from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "gms-hooks";
export default defineComponent({
  name: "DialogAddQueue",
  components:{
    FormItem,
    FormSection,
  },
  props: {
    visible: { type: Boolean, default: false },
    initOptions: { type: Object, default: () => ({}) },
    mode: { type: String, default: "add" },
  },
  emits: ["update:visible", "save-success"],
  setup(props, { emit }) {
    const t = useI18n();
    const createFormGroup = () => {
      return createFormItemGroup({
        hostCellCode: {
          type: "el-input",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.areaCode"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterAreaCode"),
          labelWidth: "110px",
          labelPosition: "top",
          validators: [validators.required],
          value: initOptions.value.hostCellCode,
        },
        areaName: {
          type: "el-input",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.areaName"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterAreaName"),
          labelWidth: "110px",
          validators: [validators.required],
          labelPosition: "top",
        },
        storageType: {
          type: "el-select",
          labelText: t("geekplus.gms.client.screen.areaEditIndex.storageAreaType"),
          placeholder: t("geekplus.gms.client.screen.areaEditIndex.enterStorageAreaType"),
          labelPosition: "top",
          options: getStorageTypesList,
          value: record.value.storageType || 10,
        },
        portChannelCount: {
          type: formItemTypes.EL_INPUT_NUMBER,
          labelText: t("geekplus.gms.client.screen.areaEditIndex.aisleQuantity"),
          precision: 0,
          value: 1,
          min: 1,
          max: 10000,
          labelPosition: "top",
          controlsPosition: "default",
        },
      });
    };

    // 将 formGroup 改为 ref
    const formGroup = ref(createFormGroup());
    const dialogTitle = computed(() => {
      if (props.mode === "add") {
        return t("geekplus.gms.client.screen.areaEditIndex.addQueueArea");
      } else if (props.mode === "edit") {
        return t("geekplus.gms.client.screen.areaEditIndex.editQueueArea");
      } else if (props.mode === "view") {
        return t("geekplus.gms.client.screen.areaEditIndex.queueAreaDetails");
      }
      return "";
    });

    return {
      dialogTitle,
    };
  },
});
</script>
