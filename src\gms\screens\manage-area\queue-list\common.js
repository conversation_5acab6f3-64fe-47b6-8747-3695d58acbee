import { columnTypes } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { confirmMsg, DELETE_BTN, ENTRY_BTN, LEAVE_BTN, ADD_BTN } from "../common.js";
import { useI18n } from "gms-hooks";
const t = useI18n();
export const getTableColumns = ({ handleDelete, handleEdit }) => [
  // 区域编码
  {
    prop: "hostCellCode",
    label: t("lang.ark.fed.areaCode"),
  },
  // 区域名称
  {
    prop: "areaName",
    label: t("lang.ark.fed.areaName"),
  },
  // 这里还差一个服务点位
  // 编辑人
  {
    prop: "updatorUsername",
    label: t("lang.ark.fed.editor"),
  },
  // 编辑时间
  {
    prop: "updateTime",
    label: t("lang.ark.fed.editingTime"),
  },
  {
    type: columnTypes.GEEK_ACTION_BUTTONS,
    displayCount: 4,
    width: 200,
    options: [
      {
        text: t("lang.ark.fed.details"),
        handleClick: ({ row }) => handleEdit(row, "view"),
      },
      {
        text: t("lang.authManage.web.common.edit"),
        handleClick: ({ row }) => handleEdit(row, "edit"),
      },
      {
        text: t("lang.ark.fed.delete"),
        confirm: {
          title: confirmMsg[`${DELETE_BTN}Content`],
          handleConfirm: ({ row }) => handleDelete(row),
        },
      },
    ],
  },
];
