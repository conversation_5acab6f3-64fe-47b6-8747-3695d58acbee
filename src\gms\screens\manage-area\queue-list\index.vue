<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <!-- 搜索框 -->
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <QueueFilter @change="handleFilterChange">
        <template #suffix>
          <ButtonEnhanced type="primary" @click="handleAdd">
            {{ $t("lang.ark.fed.addNew") }}
          </ButtonEnhanced>
        </template>
      </QueueFilter>
    </div>
    <!-- 表格数据 -->
    <table-wrapper v-bind="tableState"> </table-wrapper>
    <DialogAddQueue :visible="state.failDialogVisible" :mode="modeVal" @update:visible="handleResultConfirm" />
  </div>
</template>
<script>
import { defineComponent, onMounted, reactive, ref } from "vue";
import { ButtonEnhanced } from "gms-components";
import QueueFilter from "./queue-filter.vue";
import TableWrapper, { useTableState } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { queryAreaInfoList, deleteAreaInfo } from "gms-apis/area";
import { checkWhenDeleteNode } from "gms-apis/worksite";
import { getTableColumns } from "./common.js";
import { Message } from "geekplus-ui";
import { useI18n } from "gms-hooks";
import { GEEK_FACILITY_DICT_reserve } from "@/constants/facility";
import DialogAddQueue from "./dialog-add-queue.vue";
import { AREA_FUNC_TYPE } from "gms-constants";

export default defineComponent({
  name: "QueueList",
  components: {
    QueueFilter,
    ButtonEnhanced,
    TableWrapper,
    DialogAddQueue,
  },
  setup() {
    const { t } = useI18n();
    const state = reactive({
      failDialogVisible: false, // 控制弹框显示
      filterParams: {},
    });
    const modeVal = ref("add");

    // 表格请求函数
    const requestData = async (params) => {
      const { recordCount, recordList } = await queryAreaInfoList({
        ...params,
        ...state.filterParams,
      });
      const data = recordList.filter((item) => item.trafficFunctionType === "排队区");

      return {
        data,
        total: recordCount,
      };
    };

    // 编辑的函数
    const handleEdit = (row, model) => {
      console.log("编辑的row=====", row);
      console.log("编辑的model=====", model);
    };
    // 删除的函数
    const handleDeleteConfirm = async (row) => {
      const { cellType, id } = row;
      try {
        await checkWhenDeleteNode({
          nodeId: id,
          nodeType: GEEK_FACILITY_DICT_reserve[cellType],
        });
        await deleteAreaInfo(row.id);
        refreshTable();
        Message.success(t("lang.ark.fed.deleteSuccessfully"));
      } catch (error) {
        console.error("Delete failed:", error);
      }
    };

    const tableState = useTableState({
      columns: getTableColumns({ handleEdit, handleDelete: handleDeleteConfirm }),
      fetchData: requestData,
    });

    // 刷新表格数据
    const refreshTable = () => {
      tableState.query();
    };

    const handleFilterChange = (filterParams) => {
      state.filterParams = filterParams;
      console.log("filterParams", filterParams);
      tableState.query({ currentPage: 1 });
    };
    const handleAdd = () => {
      modeVal.value = "add";
      state.failDialogVisible = true;
    };
    // 关闭弹框的逻辑
    const handleResultConfirm = () => {
      state.failDialogVisible = false;
    };
    onMounted(() => {
      tableState.query();
    });

    return {
      state,
      tableState,
      handleFilterChange,
      handleDeleteConfirm,
      handleEdit,
      modeVal,
      handleAdd,
      handleResultConfirm,
    };
  },
});
</script>
