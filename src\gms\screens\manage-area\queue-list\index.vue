<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <!-- 搜索框 -->
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <QueueFilter @filter-change="handleFilterChange">
        <template #suffix>
          <ButtonEnhanced type="primary">
            {{ $t("lang.ark.fed.addNew") }}
          </ButtonEnhanced>
        </template>
      </QueueFilter>
    </div>
    <!-- 表格数据 -->
    <table-wrapper v-bind="tableState"> </table-wrapper>
  </div>
</template>
<script>
import { defineComponent, reactive } from "vue";
import { ButtonEnhanced } from "gms-components";
import QueueFilter from "./queue-filter.vue";
import TableWrapper, { useTableState } from "@srpe-fe/uic-el-table-wrapper-vue2";
import { queryAreaInfoList } from "gms-apis/area";
import { getTableColumns } from "./common.js";

export default defineComponent({
  name: "QueueList",
  components: {
    QueueFilter,
    ButtonEnhanced,
    TableWrapper,
  },
  setup() {
    const state = reactive({
      filterParams: {},
    });

    // 表格请求函数
    const requestData = async (params) => {
      const { recordCount, recordList } = await queryAreaInfoList({
        ...params,
        ...state.filterParams,
      });

      return {
        data: recordList,
        total: recordCount,
      };
    };
    // 编辑函数
        // 编辑的函数
    const handleEdit = (row, model) => {
      const newData = {
        ...allOptions, // 先保留原有的属性（包括 groupStrategy 等字典数据）
        ...row, // 然后用 row 的数据覆盖
        hitStrategyDict: allOptions.hitStrategyDict,
        groupStrategyDict: allOptions.groupStrategyDict,
      };

      // 使用 Object.keys 逐个更新属性，以确保触发响应式
      Object.keys(newData).forEach((key) => {
        allOptions[key] = newData[key];
      });
      modeVal.value = model; // 设置模式为编辑
      state.failDialogVisible = true; // 显示编辑对话框
    };

    const tableState = useTableState({
      columns: getTableColumns({ handleEdit, handleDelete: handleDeleteConfirm }),
      fetchData: requestData,
    });

    const handleFilterChange = (filterParams) => {
      state.filterParams = filterParams;
      tableState.query({ currentPage: 1 });
    };

    return {
      state,
      tableState,
      handleFilterChange,
    };
  },
});
</script>
